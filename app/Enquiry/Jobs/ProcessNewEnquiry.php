<?php

declare(strict_types=1);

namespace App\Enquiry\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\ApiHistoryPost;
use App\Events\CreateFollowup;
use App\Ivr\IVRWebhook\Jobs\DispatchWebhooks;
use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessNewEnquiryAutomations;
use Carbon\Carbon;
use Getlead\Campaign\Models\CampaignLead;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class ProcessNewEnquiry implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'integration:enquiry';

    public function __construct(
        public readonly int $enquiryId,
        public readonly int $vendorId,
    )
    {
    }

    public function handle(ProcessNewEnquiryAutomations $processNewEnquiryAutomations): void
    {
        Log::withContext([
            'enquiry_id' => $this->enquiryId,
            'vendor_id' => $this->vendorId,
        ]);

        Log::info('Processing new enquiry');

        $enquiry = $this->getEnquiry(vendorId: $this->vendorId, enquiryId: $this->enquiryId);

        if (!$enquiry instanceof Enquiry) {
            Log::info(message: 'Enquiry not found, skip processing');
            return;
        }

        event(new ApiHistoryPost(
            type: 1,
            enquiry_id: $this->enquiryId,
            duplicate: false,
            vendor_id: $this->vendorId,
            source_id: $enquiry->fk_int_enquiry_type_id,
            page: 2
        ));

        $processNewEnquiryAutomations->for($enquiry);

        $enquiry->refresh();
        $this->assignEnquiryToAdmin(enquiry: $enquiry, vendorId: $this->vendorId);

        Bus::dispatch(new DispatchWebhooks(
            enquiryId: $this->enquiryId,
            vendorId: $this->vendorId,
            typeId: $enquiry->fk_int_enquiry_type_id
        ));
    }

    private function getEnquiry(int $vendorId, int $enquiryId): ?Enquiry
    {
        return Enquiry::query()
            ->where('fk_int_user_id', '=', $vendorId)
            ->where('pk_int_enquiry_id', '=', $enquiryId)
            ->first();
    }

    private function assignEnquiryToAdmin(Enquiry $enquiry, int $vendorId): void
    {
        if ($enquiry->staff_id) {
            Log::info('Enquiry already assigned to a staff, skip assigning to admin', [
                'staff_id' => $enquiry->staff_id,
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'vendor_id' => $vendorId,
            ]);
            return;
        }

        if ($this->isEnquiryBelongsToCampaign($enquiry->pk_int_enquiry_id)) {
            Log::info('Enquiry belongs to a campaign, skip assigning to admin', [
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'vendor_id' => $vendorId,
            ]);
            return;
        }


        $enquiry->update([
            'staff_id' => $vendorId,
            'assigned_date' => Carbon::today()
        ]);

        $this->createFollowUp(
            lead: $enquiry,
            note: 'Admin has been designated as the lead via api.',
            vendorId: $vendorId,
            enquiryFollowUpType: EnquiryFollowup::TYPE_ACTIVITY
        );
    }

    private function createFollowUp(Enquiry $lead, string $note, int $vendorId, int $enquiryFollowUpType): void
    {
        event(new CreateFollowup($note, $enquiryFollowUpType, $lead->pk_int_enquiry_id, $vendorId));
    }

    private function isEnquiryBelongsToCampaign(int $enquiryId): bool
    {
        return CampaignLead::query()
            ->selectRaw('1')
            ->where('lead_id', $enquiryId)
            ->exists();
    }

}
