<?php

declare(strict_types=1);

namespace App\Enquiry\Listeners;

use App\Enquiry\Events\ImportRequestSanitized;
use App\Enquiry\Jobs\ProcessImport;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class StartProcessingImport implements ShouldQueue
{
    public function handle(ImportRequestSanitized $event): void
    {
        Log::withContext([
            'vendor_id' => $event->vendorId,
            'import_request_id' => $event->requestId,
        ])->info('Import request processing');

        Bus::dispatch(new ProcessImport(importRequestId: $event->requestId, vendorId: $event->vendorId));
    }
}
