<?php

declare(strict_types=1);

namespace Tests\Unit\Enquiry\Jobs;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Enquiry\Jobs\ProcessNewEnquiry;
use App\Events\ApiHistoryPost;
use App\Events\CreateFollowup;
use App\Ivr\IVRWebhook\Jobs\DispatchWebhooks;
use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessNewEnquiryAutomations;
use Getlead\Campaign\Models\CampaignLead;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

final class ProcessNewEnquiryTest extends TestCase
{
    use DatabaseTransactions;

    private ProcessNewEnquiryAutomations&MockInterface $processNewEnquiryAutomations;

    protected function setUp(): void
    {
        parent::setUp();

        Bus::fake([DispatchWebhooks::class]);
        Event::fake();

        $this->processNewEnquiryAutomations = Mockery::mock(ProcessNewEnquiryAutomations::class);
    }

    /**
     * @test
     */
    public function it_processes_new_enquiry_correctly(): void
    {
        $enquiryId = 123111111;
        $vendorId = 456;
        $enquiryTypeId = 789;
        $source = 'website';

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        $enquiry = $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->withArgs(function (Enquiry $argEnquiry) use ($enquiry): bool {
                $this->assertEquals($enquiry->pk_int_enquiry_id, $argEnquiry->pk_int_enquiry_id);
                return true;
            })
            ->andReturn(null);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertDispatched(ApiHistoryPost::class, static fn ($event) => $event->enquiry_id === $enquiryId
            && $event->vendor_id === $vendorId
            && $event->source_id === $enquiry->fk_int_enquiry_type_id
            && $event->type === 1
            && $event->duplicate === false
            && $event->page === 2);

        Bus::assertDispatched(DispatchWebhooks::class, static fn ($job) => $job->enquiryId === $enquiryId
            && $job->vendorId === $vendorId
            && $job->typeId === $enquiryTypeId);
    }

    /**
     * @test
     */
    public function it_should_skip_processing_when_enquiry_not_found(): void
    {
        $enquiryId = 123111111;
        $vendorId = 456;

        $this->processNewEnquiryAutomations->shouldNotReceive('for');

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        Log::shouldReceive('info')
            ->once()
            ->with('Enquiry not found, skip processing');

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertNotDispatched(ApiHistoryPost::class);
        Bus::assertNotDispatched(DispatchWebhooks::class);
    }

    /**
     * @test
     */
    public function it_assigns_enquiry_to_admin_when_staff_id_is_null(): void
    {
        $enquiryId = 123111111;
        $vendorId = 456;
        $enquiryTypeId = 789;

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->andReturn(null);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertDispatched(CreateFollowup::class, static fn ($event) => $event->enquiry_id === $enquiryId
            && $event->created_by === $vendorId
            && $event->note === 'Admin has been designated as the lead via api.'
            && $event->log_type === EnquiryFollowup::TYPE_ACTIVITY);

        $this->assertDatabaseHas(Enquiry::class, [
            'pk_int_enquiry_id' => $enquiryId,
            'staff_id' => $vendorId,
        ]);
    }

    /**
     * @test
     */
    public function it_does_not_assign_enquiry_to_admin_when_staff_id_already_exists(): void
    {
        $enquiryId = 123111111;
        $vendorId = 456;
        $enquiryTypeId = 789;
        $existingStaffId = 789;

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId, $existingStaffId);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->andReturnNull();

        Log::shouldReceive('info')
            ->once()
            ->with('Enquiry already assigned to a staff, skip assigning to admin', [
                'staff_id' => $existingStaffId,
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ]);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertNotDispatched(CreateFollowup::class);
        $this->assertDatabaseHas(Enquiry::class, [
            'pk_int_enquiry_id' => $enquiryId,
            'staff_id' => $existingStaffId,
        ]);
    }

    /**
     * @test
     */
    public function it_does_not_assign_enquiry_to_admin_when_it_belongs_to_campaign(): void
    {
        $enquiryId = 123111111;
        $vendorId = 456;
        $enquiryTypeId = 789;

        Log::shouldReceive('withContext')
            ->once()
            ->with([
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ])
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->once()
            ->with('Processing new enquiry');

        $this->getEnquiry($enquiryId, $vendorId, $enquiryTypeId);

        CampaignLead::query()->create([
            'lead_id' => $enquiryId,
            'campaign_id' => 1,
        ]);

        $this->processNewEnquiryAutomations->shouldReceive('for')
            ->once()
            ->andReturnNull();

        Log::shouldReceive('info')
            ->once()
            ->with('Enquiry belongs to a campaign, skip assigning to admin', [
                'enquiry_id' => $enquiryId,
                'vendor_id' => $vendorId,
            ]);

        $job = new ProcessNewEnquiry(enquiryId: $enquiryId, vendorId: $vendorId);

        $job->handle($this->processNewEnquiryAutomations);

        Event::assertNotDispatched(CreateFollowup::class);
        $this->assertDatabaseHas(Enquiry::class, [
            'pk_int_enquiry_id' => $enquiryId,
            'staff_id' => null,
        ]);
    }

    /**
     * @test
     */
    public function it_sets_queue_to_integration_enquiry(): void
    {
        $job = new ProcessNewEnquiry(enquiryId: 123, vendorId: 456);

        $this->assertEquals('integration:enquiry', $job->queue);
    }

    private function getEnquiry(int $enquiryId, int $vendorId, int $enquiryTypeId, ?int $staffId = null): Enquiry
    {
        return Enquiry::query()
            ->create([
                'pk_int_enquiry_id' => $enquiryId,
                'fk_int_user_id' => $vendorId,
                'fk_int_enquiry_type_id' => $enquiryTypeId,
                'vchr_customer_name' => 'John Doe',
                'vchr_customer_email' => '<EMAIL>',
                'vchr_customer_mobile' => '+971551234567',
                'mobile_no' => '+971551234567',
                'vchr_customer_company_name' => 'Company Name',
                'staff_id' => $staffId ?? null,
            ]);
    }
}
