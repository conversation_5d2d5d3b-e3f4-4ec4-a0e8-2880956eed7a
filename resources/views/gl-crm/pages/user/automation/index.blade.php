@extends('backend.layout.master')
@section('page-header')
@endsection
@push('header.css')
    <style>
        .btn-label{
            font-weight : 700 !important;
            font-size: 11px !important;
        }
        textarea{
            color:black;
        }
    </style>
@endpush
@section('content')
    <main class="main-wrapper">
        <div class="task-panel task-pan-Fwidth">
            <div class="row justify-content-between ">
                <div class="">
                    <h5>Automation</h5>
                </div>
                <div class="mb-2">
                    <div class="task-nav">
                        <div class="dropdown-navigation">
                            <ul>
                                <li>
                                    <a class="main-round-btn bdr1 bdr-blue mg-lft-25 clr-white"
                                       data-toggle="modal" data-target="#create_rule_modal"
                                       id="addRule" href="#"><i class="fa fa-plus"></i> Add Rule</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            {{-- @include ('backend.layout.connectsidebar') --}}
            @include ('backend.layout.sidebar-v2.glconnect-sidebar')
            <div class="content-section">
                <!--  /Your content goes here/ -->
                <div class="gl-table">
                    <table id="auto_rule_data_table" class="table table-striped table-bordered nowrap table-custom"
                        cellspacing="0" width="100%">
                        <thead>
                        <tr>
                            <th>Sl No</th>
                            <th>Name</th>
                            <th>Trigger</th>
                            <th>Action</th>
                            <th></th>
                            <th>Added BY</th>
                            <th width="10%"></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /create modal/  -------------->
    <div class="modal fade" id="create_rule_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="autoRuleAdd" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Rule</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Name</label>
                            <input type="text" name="name" autocomplete="off" class="form-control" placeholder="Name"
                                   required>
                            <span class="error name"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Trigger </label>
                            <select required class="form-control trigger_action" name="trigger" id="trigger_action">
                                <option value="">--Select--</option>
                                @foreach($triggers as $trigger)
                                  <option value="{{$trigger->value}}" data-id="{{$trigger->id}}">{{$trigger->title}}</option>
                                @endforeach

                            </select>
                            <span class="error trigger"></span>
                        </div>
                        <div class="md-form mb-1" id="auto-action">

                            <span class="error action"></span>
                        </div>
                        <div class="md-form mb-1" id="timer_value">

                        </div>
                        <div class="md-form mb-1" id="additional_fields">

                        </div>
                        <div class="md-form mb-1" id="additional_values">

                        </div>


                        <div class="md-form mb-1" id="assign_mode">
                            <label>Assign to Specific</label>
                            <select  class="form-control assigned"   name="assign_mode"  id="assigned">
                                <option value="">--select--</option>
                                <option value="1">Staff</option>
                                <option value="2">Department</option>
                            </select>
                        </div>

                        <div class="md-form mb-1" id="web_hooks_assign">

                        </div>

                        <div class="md-form mb-1" id="web_hooks">

                        </div>

                        <div class="md-form mb-1 d-none" id="campaign_div">

                        </div>
                        <div id="email_template_list">

                        </div>

                    </div>
                    <div class="modal-footer d-flex justify-content-center" >
                    <span id="scratch_param" style="display:none;">* For Scratch and Lucky Draw API Parameters: <br />
                    {number} for Mobile Number, <br />{redeem_id} for Redeem ID, <br />
                    {name} for Customer Name, <br />{bill_no} for Bill Number</span>

                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <button class="main-round-btn"><i class="fa fa-plus"></i> Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal fade" id="edit_modal_rule" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="autoRuleEdit" enctype="multipart/form-data">
            <div class="modal-dialog" role="document">
                {{method_field('PUT')}}
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Update Rule</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Name</label>
                            <input type="text" name="name" autocomplete="off" id="rule_name" class="form-control"
                                   placeholder="Name"
                                   required>
                            <span class="error name"></span>
                        </div>
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Trigger </label>
                            <select required class="form-control" name="trigger" id="trigger_type">
                                <option value="">--Select--</option>
                                @foreach($triggers as $trigger)
                                <option value="{{$trigger->value}}" data-id="{{$trigger->id}}">{{$trigger->title}}</option>
                               @endforeach

                            </select>
                            <span class="error trigger"></span>
                        </div>

                        <div class="md-form mb-1" id="timer_value_edit">

                        </div>
                        <div class="md-form mb-1" id="additional_fields_edit">

                        </div>
                        <div class="md-form mb-1" id="additional_values_edit">

                        </div>
                        <div class="md-form mb-1" id="auto_rule_action">

                        </div>
                        <input type="hidden" id="edit_auto_trigger">
                        <div class="md-form mb-1" id="rule_assign_mode">
                            <label>Assign to Specific</label>
                            <select  class="form-control assigned"   name="rule_assign_mode"  id="rule_assign_mode_id">
                                <option value="">--select--</option>
                                <option value="1">Staff</option>
                                <option value="2">Department</option>
                            </select>
                        </div>
                        <div class="md-form mb-1" id="web_hooks_edit">

                        </div>
                        <div class="md-form mb-1" id="web_hooks_edit_assign">

                        </div>
                        <div class="md-form mb-1 d-none" id="campaign_div_edit">

                        </div>
                        <div class="md-form mb-1" id="web_email_edit">

                        </div>

                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <span id="editscratch_param" style="display: none;">* For Scratch and Lucky Draw API Parameters: <br />{number} for Mobile Number, <br />{redeem_id} for Redeem ID, <br />{name} for Customer Name, <br />{bill_no} for Bill Number</span>
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <input type="hidden" id="autorule_id">
                        <button class="main-round-btn"><i class="fa fa-plus"></i> Update</button>
                    </div>
                </div>
            </div>
        </form>
    </div>


@endsection
@push('footer.script')

    <script type="text/javascript">
     $('.summernote-1').summernote({
        height: '215',

    });
        BASE_URL = {!! json_encode(url('/')) !!};
        var additional_field = @json($additional_fields)

        function readURL1(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#profile-img-tag').attr('src', e.target.result);
                    $('#profile-img-tag').hide();
                    $('#profile-img-tag').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        function readURL(input) {

            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#blah').attr('src', e.target.result);
                    $('#blah').hide();
                    $('#blah').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }


        $(document).ready(function () {
            $('.error').hide();
            $('#assign_mode').hide();
            $('#rule_assign_mode').hide();
            $('#web_hooks_mail').html('');
            $('#email_template_list').html('');

            $('#trigger_type').on('change',function (){
                var trigger_id = $(this).find(':selected').data('id');
                $.ajax({
                        url: BASE_URL + '/user/list-automation-actions/?trigger=' + trigger_id,
                        type: 'GET',
                        dataType: 'JSON',

                        processData: false,
                    }).done(function (res) {

                        var select_fld = '<label for="default-input" class="form-control-label">Action </label><select required class="form-control" name="action" id="rule_action">' +
                                '<option value="">Select</option>';
                        $.each(res.data, function (index, value) {
                            select_fld = select_fld + '<option value="' + value.value + '">' + value.title + '</option>';
                        });
                        select_fld = select_fld + '</select>';
                        $('#auto_rule_action').html(select_fld);

                        var action=$('#edit_auto_trigger').val();
                        $("#rule_action").val(action).trigger('change');
                    });

                if($('#trigger_type').val() =='new_scratch')
                {
                    $('#editscratch_param').show();
                }
                else
                {
                    $('#editscratch_param').hide();
                }
            })

            $('.trigger_action').on('change', function () {

                var trigger_id = $(this).find(':selected').data('id');
                $.ajax({
                        url: BASE_URL + '/user/list-automation-actions/?trigger=' + trigger_id,
                        type: 'GET',
                        dataType: 'JSON',

                        processData: false,
                    }).done(function (res) {

                     var select_fld = '<label for="default-input" class="form-control-label">Action </label><select required class="form-control" name="action" id="actions">' +
                                '<option value="">Select</option>';

                            $.each(res.data, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.value + '">' + value.title + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#auto-action').html(select_fld);
                    });
            });

            // $('#actions').on('change', function () {
            $(document).on('change', '#actions', function () {
                $('#assign_mode').hide();
                $('#web_hooks').html('');
                $('#web_hooks_mail').html('');
                $('#email_template_list').html('');
                $('#campaign_div').addClass('d-none')
                // $('#additional_fields').html('');
                var action = $(this).val();
                /*-------------ACTION_WEBHOOK_START-------------*/
                if (action == "webhook") {
                    $('.email_params').addClass('d-none')
                    $.ajax({
                        url: BASE_URL + '/user/webhook-list/',
                        type: 'GET',
                        dataType: 'JSON',

                        processData: false,
                    }).done(function (res) {
                        if (res.status == true) {
                            var select_fld = '<label>Webhook</label><select  class="form-control"  required name="webhook_id" id="web_hooks_list">' +
                                '<option value="">Select</option>';

                            $.each(res.data.webhooks, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#web_hooks').append(select_fld);
                            if ($('#trigger_action').val() == 'status_change') {
                               statusCreation(res.data.status);
                            }
                            $.ajax({
                                url: BASE_URL + '/user/automation-data/?action=api',
                                type: 'GET',
                                dataType: 'JSON',
                                processData: false,
                            }).done(function (res) {
                                var source = res.data.source;
                                var purpose = res.data.purpose;
                                var status = res.data.status;
                                switch ($('#trigger_action').val()) {
                                    case 'status_change':
                                        statusCreation(status);
                                        break;
                                    case 'source_change':
                                        sourceCreation(source);
                                        break;
                                    case 'purpose_change':
                                        purposeCreation(purpose);
                                        break;
                                    case 'new_lead':
                                        sourceCreation(source,true);
                                        break;
                                    default:
                                        break;
                                }
                            });
                        } else {
                            $.alert({
                                title: 'Failed',
                                type: 'red',
                                content: res.msg,
                            });
                        }
                    });
                }
                /*-------------ACTION_WEBHOOK_END-----------------*/
                /*-------------ACTION_WHATSAPP_START-------------*/
                else if (action == "whatsapp") {

                    $('.email_params').addClass('d-none')
                    $.ajax({
                        url: BASE_URL + '/user/automation-data/?action=' + action,
                        type: 'GET',
                        dataType: 'JSON',
                        processData: false,
                    }).done(function (res) {

                        var status = res.data.status;
                        var action = res.data.action;
                        var source = res.data.source;
                        var purpose = res.data.purpose;
                        var service = res.data.service;

                        if ($('#trigger_action').val() == 'status_change') {
                            statusCreation(status);
                        }
                        if ($('#trigger_action').val() == 'service_remider') {
                            serviceWithReminderTrigger(service);
                        }
                        else if ($('#trigger_action').val() == 'purpose_change') {
                            purposeCreation(purpose);
                        }
                        else {
                            sourceCreation(source);
                        }

                        whatsappTemplateGeneration(action);
                    });

                }
                /*-------------ACTION_WHATSAPP_END-------------*/
                /*-------------ACTION_API_START-------------*/
                else if (action == "api") {
                    $('.email_params').addClass('d-none')
                    $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=' + action,
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {
                            if ($('#trigger_action').val() == 'status_change') {
                                var status = res.data.status;
                                statusWithApi(status);
                           } else if ($('#trigger_action').val() == 'purpose_change') {
                                var purpose = res.data.purpose;
                                purposeWithApi(purpose,true);
                           } else {
                                var source = res.data.source;
                                sourceWithApi(source,true);
                            }
                       });
                }
                /*-------------ACTION_API_END-------------*/
                /*-------------ACTION_TASK_START-------------*/
                else if (action == "task") {
                    $('.email_params').addClass('d-none')
                    if ($('#trigger_action').val() == '') {
                        alert('please select action');
                    } else {
                        getAutomationData(action, $('#trigger_action').val());
                    }
                }
                /*-------------ACTION_EMAIL_START-------------*/
                else if (action == "email") {
                    if ($('#trigger_action').val() == '') {
                        alert('please select action');
                    } else {
                        $('.email_params').removeClass('d-none')
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=api',
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {

                            if ($('#trigger_action').val() == 'status_change') {
                                var status = res.data.status;
                                var template = res.data.email_template;
                                statusCreation(status);
                                var select_fld_value = '<label>Choose Template</label><select class="form-control select2" name="emplate_id" id="emplate_id">' +
                                    '<option value="">---Select Template---</option>';
                                    $.each(template, function (index, value) {

                                        select_fld_value = select_fld_value + '<option value="' + value.id + '">' + value.template_title + '</option>';
                                    });
                                    select_fld_value = select_fld_value + '</select>';
                                    $('#email_div').addClass('d-none');
                                    $('#email_template_list').append(select_fld_value);
                            } else if ($('#trigger_action').val() == 'purpose_change') {
                                var purpose = res.data.purpose;
                                var template = res.data.email_template;
                                purposeCreation(purpose);
                                var select_fld_value = '<label>Choose Template</label><select class="form-control select2" name="emplate_id" id="emplate_id">' +
                                    '<option value="">---Select Template---</option>';
                                    $.each(template, function (index, value) {

                                        select_fld_value = select_fld_value + '<option value="' + value.id + '">' + value.template_title + '</option>';
                                    });
                                    select_fld_value = select_fld_value + '</select>';
                                    $('#email_div').addClass('d-none');
                                    $('#email_template_list').append(select_fld_value);
                            }
                        });
                    }
                }
                else if (action == "assign") {
                    $('.email_params').addClass('d-none')
                    if ($('#trigger_action').val() == '') {
                        alert('please select action');
                    } else {
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=api',
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {

                            if ($('#trigger_action').val() == 'status_change') {
                                var status = res.data.status;
                                statusCreation(status);
                            }
                            if ($('#trigger_action').val() == 'source_change' || $('#trigger_action').val()=='new_lead' ) {
                                var source = res.data.source;
                                sourceCreation(source,true);
                                
                                // Add purpose dropdown for new_lead trigger
                                if ($('#trigger_action').val() == 'new_lead') {
                                    var purpose = res.data.purpose;
                                    purposeCreation(purpose, true);
                                }
                            }
                            if ($('#trigger_action').val() == 'purpose_change') {
                                var purpose = res.data.purpose;
                                purposeCreation(purpose,true);
                            }
                            if($('#trigger_action').val() == 'value_change'){
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',
                                    processData: false,
                                }).done(function (res) {
                                        var source = res.data.source;
                                        sourceCreation(source);
                                });
                            }

                        });

                        $('#assign_mode').show();

                    }
                }
                /*------------- ACTION ADD TO CAMPAIGN ----------*/
                else if (action == "add_to_campaign") {
                    if ($('#trigger_action').val() == '') {
                        alert('please select action');
                    } else {
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=campaign',
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {
                            if ($('#trigger_action').val() == 'new_lead' || $('#trigger_action').val()=='source_change') {
                                $('#campaign_div').text('')
                                $('#campaign_div').removeClass('d-none')
                                var source = res.data.source;
                                var campaign = res.data.campaign;
                                var type='campaign';
                                campaignDivCreation(campaign,type)
                                sourceCreation(source);
                            }
                            if ($('#trigger_action').val() == 'purpose_change') {
                                $('#campaign_div').text('')
                                $('#campaign_div').removeClass('d-none')
                                var purpose = res.data.purpose;
                                var campaign = res.data.campaign;
                                var type='campaign';
                                campaignDivCreation(campaign,type)
                                purposeCreation(purpose);
                            }
                        });


                    }
                }
                 /*------------- ACTION ADD TO Data Pool ----------*/
                else if (action == "add_to_data_pool") {
                    if ($('#trigger_action').val() == '') {
                        alert('please select action');
                    } else {
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=data_pool',
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {
                            if ($('#trigger_action').val() == 'new_lead') {
                                $('#campaign_div').text('')
                                $('#campaign_div').removeClass('d-none')
                                var source = res.data.source;
                                var campaign = res.data.campaign;
                                var type='pool';
                                campaignDivCreation(campaign,type)
                                sourceCreation(source);
                            }
                            if ($('#trigger_action').val() == 'purpose_change') {
                                $('#campaign_div').text('')
                                $('#campaign_div').removeClass('d-none')
                                var purpose = res.data.purpose;
                                var campaign = res.data.campaign;
                                var type='pool';
                                campaignDivCreation(campaign,type)
                                purposeCreation(purpose);
                            }
                        });


                    }
                }
                else if (action == 'unassign') {
                    $('.email_params').addClass('d-none')
                    $.ajax({
                        url: BASE_URL + '/user/automation-data/?action=api',
                        type: 'GET',
                        dataType: 'JSON',
                        processData: false,
                    }).done(function (res) {

                        var status = res.data.status;
                        var action = res.data.action;
                        if ($('#trigger_action').val() == 'timer' ) {
                            var select_fld = '<label>Status</label><select  class="form-control select2"  required name="feedback_status_id[]" id="feedback_status_list" multiple>' +
                                '<option value="">Select Status</option>';
                            $.each(status, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#web_hooks').append(select_fld);
                            $('#feedback_status_list').select2();
                        }
                        if ($('#trigger_action').val() == 'reassign' ) {
                            var select_fld = '<label>Status</label><select  class="form-control select2"  required name="feedback_status_id" id="feedback_status_list" >' +
                                '<option value="">Select Status</option>';
                            $.each(status, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#web_hooks').append(select_fld);

                        }
                    });
                }
                else if(action == 'report') {
                    $.ajax({
                        url: BASE_URL + '/user/automation-data/?action=api',
                        type: 'GET',
                        dataType: 'JSON',
                        processData: false,
                    }).done(function (res) {

                        var status = res.data.status;

                        var select_fld = '<label>Type</label><select  class="form-control select2"  required name="notification_type" id="type" >' +
                                '<option value="">Select Type</option><option value="1">With Out Followup Summary</option></select>';


                        select_fld = select_fld + '<label>Status</label><select  class="form-control select2"  required name="feedback_status_id[]" id="feedback_status_list" multiple>' +
                                '<option value="">Select Status</option>';
                            $.each(status, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#web_hooks').append(select_fld);
                            $('#feedback_status_list').select2();

                    });

                }

                /*-------------ACTION_API_END-------------*/
            })

             $('.assigned').on('change', function () {
                // function assignData(){
                 var mode = $(this).val();

                $('#web_hooks_assign').html('');
                $.ajax({
                    url: BASE_URL + '/user/getAssignData/' + mode,
                    type: 'GET',
                    dataType: 'JSON',
                    processData: false,
                }).done(function (res) {
                    console.log(res);
                    $('#web_hooks_edit_assign').html('');
                    if (mode == 1) {
                        var title = 'Staff';
                    } else {
                        var title = 'Department';
                    }
                    var service = res.data;
                    var select_fld = '<label>' + title + '</label><select  class="form-control select2"   name="assign_id"  id="assign_id">' +
                        '<option value="">--select--</option>';
                    var edit_select_fld = '<label>' + title + '</label><select  class="form-control select2"   name="rule_assign_id"  id="rule_assign_id">' +
                        '<option value="">--select--</option>';
                    $.each(service, function (index, value) {
                        select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                        edit_select_fld = edit_select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                    });
                    select_fld = select_fld + '</select>';
                    edit_select_fld = edit_select_fld + '</select>';

                    $('#web_hooks_assign').append(select_fld);
                    $('#web_hooks_edit_assign').append(edit_select_fld);

                });
            })
            function assignData(mode,assign_id) {
                $('#web_hooks_edit_assign').html('');
                $.ajax({
                    url: BASE_URL + '/user/getAssignData/' + mode,
                    type: 'GET',
                    dataType: 'JSON',
                    processData: false,
                }).done(function (res) {
                    if (mode == 1) {
                        var title = 'Staff';
                    } else {
                        var title = 'Department';
                    }
                    var service = res.data;
                    var select_fld = '<label>' + title + '</label><select  class="form-control select2"   name="rule_assign_id"  id="rule_assign_id">' +
                        '<option value="">--select--</option>';
                    var sel='';
                    $.each(service, function (index, value) {
                        if(value.id==assign_id)
                        {
                            select_fld = select_fld + '<option selected value="' + value.id + '">' + value.name + '</option>';

                        }
                        else{
                            select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                        }
                        console.log(sel);
                    });
                    select_fld = select_fld + '</select>';

                    $('#web_hooks_edit_assign').append(select_fld);

                });
            }

            $('#trigger_action').on('change', function () {

                resetAdditionalFields();
                if($('#trigger_action').val() =='value_change')
                {
                    var select_fld = '<label>Choose Field</label><select class="form-control select2" name="field" id="field">' +
                                '<option value="">---Select Field---</option>';
                    $.each(additional_field, function (index, value) {
                        select_fld = select_fld + '<option value="' + value.id + '">' + value.field_name + '</option>';
                    });
                    select_fld = select_fld + '</select>';
                    $('#additional_fields').append(select_fld);
                    $('#additional_fields').show();
                }
                else
                {
                    $('#additional_fields').hide();
                }

                if($('#trigger_action').val() =='new_scratch')
                {
                    $('#scratch_param').show();
                }
                else
                {
                    $('#scratch_param').hide();
                }

                $('#web_hooks').html('');
                if ($('#actions').val() == "api") {
                    if ($('#trigger_action').val() == 'status_change') {
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=' + $('#trigger_action').val(),
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {
                            var status = res.data.status;
                            statusWithApi(status);
                        });
                    }
                    if ($('#trigger_action').val() == 'service_remider') {
                        $.ajax({
                            url: BASE_URL + '/user/automation-data/?action=' + $('#trigger_action').val(),
                            type: 'GET',
                            dataType: 'JSON',
                            processData: false,
                        }).done(function (res) {
                            var status = res.data.status;
                            var service= res.data.service;
                            var select_fld = '<label>Service</label><select  class="form-control select2"   name="service[]" multiple id="service">' +
                                '<option value="">Select Service</option><option value="0">All</option>';
                            $.each(service, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            var fld = '<label>API</label><input type="text" autocomplete="off" placeholder="API" class="form-control" name="api">';

                            var select_fld3 = '<label>Reminder before/after</label><select  class="form-control"  required name="reminder_type" id="reminder_type">' +
                                '<option value="">Select Reminder </option><option value="-">Before Day</option><option value="+">After Day</option>';
                            var fld2 = '<label>Days</label><input type="number" class="form-control" name="days" value="' + data.log_day + '">';

                            $('#web_hooks').append(select_fld);
                            $('#web_hooks').append(fld);
                            $('#web_hooks').append(select_fld3);
                            $('#web_hooks').append(fld2);
                            $('#service').select2();
                        });
                    }
                    else {
                        var fld = '<label>API</label><input type="text" autocomplete="off" placeholder="API" class="form-control" name="api">';
                        $('#web_hooks').append(fld);
                    }
                }

                if($('#trigger_action').val() =='timer' || $('#trigger_action').val() =='reassign') {
                    var select_fld = '<label>Enter timer (in hours)</label>\
                                <input type="number" class="form-control" name="timer_input" id="timer_input">';

                    select_fld = select_fld + '</select>';
                    $('#timer_value').append(select_fld);
                    $('#timer_value').show();
                }


            });
            $('#rule').on('change', function () {
                $('#web_hooks').html('');
                var action = $(this).val();
                if (action == "webhook") {
                    $.ajax({
                        url: BASE_URL + '/user/webhook-list/',
                        type: 'GET',
                        dataType: 'JSON',

                        processData: false,
                    }).done(function (res) {
                        if (res.status == true) {
                            var select_fld = '<select  class="form-control"  required name="webhook_id" id="web_hooks_list">' +
                                '<option value="">Select</option>';

                            $.each(res.data, function (index, value) {
                                select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                            });
                            select_fld = select_fld + '</select>';
                            $('#web_hooks').append(select_fld);
                        } else {
                            $.alert({
                                title: 'Failed',
                                type: 'red',
                                content: res.msg,
                            });
                        }
                    });
                }
            })

            /*-----Get Data------*/
            $('#auto_rule_data_table').DataTable({
                scrollX: true,
                paging: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                ajax: BASE_URL + '/user/get-rules',
                columns: [
                    {data: 'slno', name: 'slno'},
                    {data: 'rule_name', name: 'rule_name'},
                    {data: 'trigger_type', name: 'trigger_type'},
                    {data: 'rule_action', name: 'rule_action'},
              //      {data: 'url', name: 'url'},
                    {data: 'we_name', name: 'we_name'},
                    {data: 'added_by', name: 'added_by'},
                    {data: 'show', name: 'show', orderable: false, searchable: false}
                ],
            });
            /*-----Add------*/
            $(document).on('submit', '#autoRuleAdd', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/automation',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            $('#create_rule_modal').modal('toggle');
                            $.alert({
                                title: 'Success',
                                type: 'green',
                                content: res.msg,
                            });
                            $('#auto_rule_data_table').DataTable().ajax.reload(null, false);

                        }
                        if (res.status == 'exists') {
                            $.alert({
                                title: 'Failed',
                                type: 'red',
                                content: res.msg,
                            });
                        } else {
                            $.each(res.msg, function (index, val) {
                                $('.' + index).html(val);
                                $('.' + index).show();
                            });
                        }
                    }).fail(function () {
                }).always(function (com) {
                });
            });
            /*-----Update------*/
            $(document).on('submit', '#autoRuleEdit', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                var id = $('#autorule_id').val();
                var dd = new FormData(this);
                $.ajax({
                    url: BASE_URL + '/user/automation/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#edit_modal_rule').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                    } else {
                        $.each(res.msg, function (index, val) {
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {
                }).always(function (com) {
                    $('#auto_rule_data_table').DataTable().ajax.reload(null, false);
                });
            });

            $('#auto_rule_data_table').on('click', '.rule-delete', function (event) {
                event.preventDefault();
                var id = $(this).attr('rule_id');
                var destinationPath = BASE_URL + '/user/automation/' + id;
                $.confirm({
                    title: 'Deletion',
                    content: 'Are you sure you want to delete ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: destinationPath,
                                    type: 'DELETE',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                })
                                    .fail(function (err) {
                                    })
                                    .always(function (com) {
                                        $('#auto_rule_data_table').DataTable().ajax.reload(null, false);
                                    });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            })
            /*-----View------*/
            $('#auto_rule_data_table').on('click', '.rule_edit', function (event) {
                var ruleId = $(this).attr('data-rule-id');
                $('#rule_assign_mode').hide();

                $.ajax({
                    url: BASE_URL + '/user/automation/' + ruleId,
                    type: 'GET',
                    dataType: 'JSON',
                }).done(function (res) {

                    if (res.status == "success") {
                        var data = res.data;

                        $("#rule_name").val(data.rule_name);
                        $("#autorule_id").val(data.id);
                        // $("#rule_action").val(data.action).trigger('change');
                        $("#edit_auto_trigger").val(data.action);
                        $("#trigger_type").val(data.trigger).trigger('change');
                        $('#web_hooks_edit').html('');
                        $('#web_email_edit').html('');
                        $('#additional_fields_edit').html('');
                        $('#additional_values_edit').html('');
                        $('#email_template_code').html(data.email_template);
                        $('#email_div_edit').addClass('d-none')
                        $('#campaign_div_edit').addClass('d-none')
                        if(data.trigger =='timer' || data.trigger =='reassign') {
                            $('#timer_value_edit').html('');
                            var select_fld = '<label>Enter timer (in hours)</label>\
                                        <input type="number" class="form-control" name="timer_input_edit" id="timer_input_edit" value="'+data.duration+'">\
                                        </select>';
                            $('#timer_value_edit').append(select_fld);
                            $('#timer_value_edit').show();
                        }

                        if(data.action == "webhook") {
                            $.ajax({
                                url: BASE_URL + '/user/webhook-list/',
                                type: 'GET',
                                dataType: 'JSON',

                                processData: false,
                            }).done(function (res) {
                                if (res.status == true) {
                                    var select_fld = '<label>Weenhooks</label><select  class="form-control"  required name="webhook_id" id="web_hooks_list">' +
                                        '<option value="">Select</option>';

                                    $.each(res.data.webhooks, function (index, value) {
                                        if (value.id == data.webhook_id) {
                                            select_fld = select_fld + '<option selected value="' + value.id + '">' + value.name + '</option>';

                                        } else {
                                            select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';

                                        }
                                    });
                                    select_fld = select_fld + '</select>';
                                    var source = res.data.source;
                                        var source_field = '<label>Source</label><select  class="form-control" name="enquiry_source_id" id="source_list">' +
                                            '<option value="">Select source</option>';
                                        $.each(source, function (index, value) {
                                            if (value.id == data.enquiry_source_id) {
                                                source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            } else {
                                                source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            }
                                        });

                                    $('#web_hooks_edit').append(select_fld);
                                    $('#web_hooks_edit').append(source_field);
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            });
                        } else if(data.action == "whatsapp") {
                            $.ajax({
                                url: BASE_URL + '/user/automation-data/?action=' + data.action,
                                type: 'GET',
                                dataType: 'JSON',

                                processData: false,
                            }).done(function (res) {
                                if (res.status == true) {

                                    var status = res.data.status;
                                    var action = res.data.action;
                                    var source = res.data.source;
                                    var source_field = '<label>Enquiry Source</label><select  class="form-control"  required name="enquiry_source_id" id="whatsapp_template_list">' +
                                        '<option value="">Select </option>';
                                    $.each(source, function (index, value) {
                                        source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                    });
                                    var select_fld = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                        '<option value="">Select Status</option>';
                                        $.each(status, function (index, value) {
                                            if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                                select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                            } else {
                                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';

                                            }
                                        });
                                    select_fld = select_fld + '</select>';

                                    var select_fld2 = '<label>Whatsapp Template</label><select  class="form-control"  required name="whatsapp_template_id" id="whatsapp_template_list">' +
                                        '<option value="">Select Template</option>';
                                    $.each(action, function (index, value) {
                                        if (value.id == data.whatsapp_template_id) {
                                            select_fld2 = select_fld2 + '<option selected value="' + value.id + '">' + value.vchr_whatsapp_template_title + '</option>';
                                        } else {
                                            select_fld2 = select_fld2 + '<option value="' + value.id + '">' + value.vchr_whatsapp_template_title + '</option>';
                                        }


                                    });
                                    select_fld2 = select_fld2 + '</select>';
                                    var fld2 = '<label>Days</label><input type="number" class="form-control" name="days" value="' + data.log_day + '">';

                                    $('#web_hooks_edit').append(select_fld);
                                    $('#web_hooks_edit').append(select_fld2);
                                    $('#web_hooks_edit').append(source_field);
                                    $('#web_hooks_edit').append(fld2);

                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            });
                        } else if(data.action == "api") {
                            if (data.trigger == "status_change") {
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=' + data.action,
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {
                                    if (res.status == true) {
                                        var status = res.data.status;
                                        var action = res.data.action;
                                        var select_fld = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                            '<option value="">Select Status</option>';
                                        $.each(status, function (index, value) {
                                            if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                                select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                            } else {
                                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';

                                            }
                                        });
                                        select_fld = select_fld + '</select>';
                                        $('#web_hooks_edit').append(select_fld);
                                    }
                                });
                                var fld2 = '<label>API</label><input type="text" autocomplete="off" required class="form-control" name="api" value="' + data.api + '">';
                                $('#web_hooks_edit').append(fld2);
                            } 
                            else if(data.trigger == "new_lead"){
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var source = res.data.source;
                                        var source_field = '<label>Source</label><select  class="form-control" name="enquiry_source_id" id="source_list">' +
                                            '<option value="">Select source</option>';
                                        $.each(source, function (index, value) {
                                            if (value.id == data.enquiry_source_id) {
                                                source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            } else {
                                                source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            }
                                        });
                                        $('#web_hooks_edit').append(source_field);
                                    }
                                });
                                var fld2 = '<label>API</label><input type="text" autocomplete="off" required class="form-control" name="api" value="' + data.api + '">';
                                $('#web_hooks_edit').append(fld2);
                            }
                            else{
                                var fld = '<label>API</label><input type="text" required autocomplete="off" class="form-control" name="api" value="' + data.api + '">';
                                $('#web_hooks_edit').append(fld);
                            }

                        } else if(data.action == "task") {
                            $.ajax({
                                url: BASE_URL + '/user/automation-data/?action=' + data.action,
                                type: 'GET',
                                dataType: 'JSON',
                                processData: false,
                            }).done(function (res) {

                                if (data.trigger == 'status_change') {
                                    var status = res.data.status;
                                    var status_field = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                        '<option value="">Select Status</option>';
                                    $.each(status, function (index, value) {
                                        if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                            status_field = status_field + '<option selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                        } else {
                                            status_field = status_field + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(status_field);
                                }
                                if (data.trigger == 'source_change') {
                                    var source = res.data.source;
                                    var source_field = '<label>Source</label><select  class="form-control"  required name="enquiry_source_id" id="source_list">' +
                                        '<option value="">Select source</option>';
                                    $.each(source, function (index, value) {
                                        if (value.id == data.enquiry_source_id) {
                                            source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        } else {
                                            source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(source_field);
                                }
                                if (data.action == "task") {
                                    var t_category = res.data.action;
                                    var select_fld = '<label>Task Category </label>' +
                                        '<select  class="form-control"  required name="task_category_id" id="task_category_list">' +
                                        '<option value="">Select Task Category</option>';
                                    $.each(t_category, function (index, value) {
                                        if (value.id == data.task_category_id) {
                                            select_fld = select_fld + '<option selected value="' + value.id + '" >' + value.name + '</option>';
                                        } else {
                                            select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
                                        }
                                    });
                                    select_fld = select_fld + '</select>';
                                    var fld = '<label>Task Title</label><input type="text" autocomplete="off" value="' + data.task_title + '" placeholder="Title" class="form-control" name="task_title">' +
                                        '<label>Task Description</label><textarea type="text" autocomplete="off"  placeholder="Description" class="form-control" name="task_description">' + data.task_title + '</textarea>';
                                    var agents = res.data.agents;
                                    var agent_field = '<label>Agent</label><select  class="form-control"  required name="task_assigned_to" id="task_assigned_to_list">' +
                                        '<option value="">Select Agent</option>';
                                    $.each(agents, function (index, value) {
                                        if (value.pk_int_user_id == data.task_assigned_to) {
                                            agent_field = agent_field + '<option selected value="' + value.pk_int_user_id + '">' + value.vchr_user_name + '</option>';
                                        } else {
                                            agent_field = agent_field + '<option value="' + value.pk_int_user_id + '">' + value.vchr_user_name + '</option>';

                                        }
                                    });
                                    var durnt='<label>Duration(minutes)</label><input type="number" class="form-control" name="duration" value="'+data.duration+'">';
                                    $('#web_hooks_edit').append(agent_field);
                                    $('#web_hooks_edit').append(select_fld);
                                    $('#web_hooks_edit').append(durnt);
                                }
                                if (data.trigger == "value_change") {
                                    $('#additional_values_edit').html('');
                                    $('#additional_fields_edit').html('');
                                    var select_fld = '';
                                    var select_fld = '<label>Choose Field</label><select class="form-control select2" name="field_edit" id="field_edit">' +
                                    '<option value="">---Select Field---</option>';
                                    $.each(additional_field, function (index, value) {
                                        if(data.additional_field == value.id)
                                            select_fld = select_fld + '<option value="' + value.id + '" selected>' + value.field_name + '</option>';
                                        else
                                            select_fld = select_fld + '<option value="' + value.id + '">' + value.field_name + '</option>';
                                    });
                                    select_fld = select_fld + '</select>';


                                    var select_fld_value = '<label>Choose Value</label><select class="form-control select2" name="field_value_edit" id="field_value_edit">' +
                                                    '<option value="">---Select Value---</option>';
                                    $.each(additional_field, function (index, value) {
                                        if(value.id == data.additional_field){
                                            $.each(value.values, function (index, value) {
                                                if(data.additional_field_value == value)
                                                    select_fld_value = select_fld_value + '<option value="' + value + '" selected>' + value + '</option>';
                                                else
                                                    select_fld_value = select_fld_value + '<option value="' + value + '">' + value + '</option>';
                                            });
                                        }
                                    });
                                    select_fld_value = select_fld_value + '</select>';
                                    $('#additional_values_edit').append(select_fld_value);
                                    $('#additional_fields_edit').append(select_fld);
                                }

                                $('#web_hooks_edit').append(select_fld);
                                $('#web_hooks_edit').append(fld);

                            });
                        } else if(data.action == "assign") {
                            if (data.trigger == "status_change") {
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var status = res.data.status;
                                         var template = res.data.email_template;
                                        var select_fld = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                            '<option value="">Select Status</option>';
                                        $.each(status, function (index, value) {
                                            if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                                select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                            } else {
                                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';

                                            }
                                        });
                                        select_fld = select_fld + '</select>';

                                        $('#web_hooks_edit').append(select_fld);

                                        }
                                });

                                $("#rule_assign_mode_id").val(data.assign_mode);
                                $('#rule_assign_mode').show();


                                assignData(data.assign_mode,data.assign_id);
                            }
                            if (data.trigger == "source_change") {
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var source = res.data.source;
                                    var source_field = '<label>Source</label><select  class="form-control"  required name="enquiry_source_id" id="source_list">' +
                                        '<option value="">Select source</option>';
                                    $.each(source, function (index, value) {
                                        if (value.id == data.enquiry_source_id) {
                                            source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        } else {
                                            source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(source_field);
                                    }
                                });

                                $("#rule_assign_mode_id").val(data.assign_mode);
                                $('#rule_assign_mode').show();
                            }
                            if (data.trigger == "purpose_change") {
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var purpose = res.data.purpose;
                                    var purpose_field = '<label>Purpose</label><select  class="form-control"  required name="enquiry_purpose_id" id="purpose_list">' +
                                        '<option value="">Select purpose</option>';
                                    $.each(purpose, function (index, value) {
                                        if (value.id == data.enquiry_purpose_id) {
                                            purpose_field = purpose_field + '<option selected value="' + value.id + '">' + value.vchr_purpose + '</option>';
                                        } else {
                                            purpose_field = purpose_field + '<option value="' + value.id + '">' + value.vchr_purpose + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(purpose_field);
                                    }
                                });

                                $("#rule_assign_mode_id").val(data.assign_mode);
                                $('#rule_assign_mode').show();


                                assignData(data.assign_mode,data.assign_id);
                            }
                            if (data.trigger == "value_change") {
                                $('#additional_values_edit').html('');
                                $('#additional_fields_edit').html('');
                                var select_fld = '';
                                var select_fld = '<label>Choose Field</label><select class="form-control select2" name="field_edit" id="field_edit">' +
                                '<option value="">---Select Field---</option>';
                                $.each(additional_field, function (index, value) {
                                    if(data.additional_field == value.id)
                                        select_fld = select_fld + '<option value="' + value.id + '" selected>' + value.field_name + '</option>';
                                    else
                                        select_fld = select_fld + '<option value="' + value.id + '">' + value.field_name + '</option>';
                                });
                                select_fld = select_fld + '</select>';


                                var select_fld_value = '<label>Choose Value</label><select class="form-control select2" name="field_value_edit" id="field_value_edit">' +
                                                '<option value="">---Select Value---</option>';
                                $.each(additional_field, function (index, value) {
                                    if(value.id == data.additional_field){
                                        $.each(value.values, function (index, value) {
                                            if(data.additional_field_value == value)
                                                select_fld_value = select_fld_value + '<option value="' + value + '" selected>' + value + '</option>';
                                            else
                                                select_fld_value = select_fld_value + '<option value="' + value + '">' + value + '</option>';
                                        });
                                    }
                                });
                                select_fld_value = select_fld_value + '</select>';
                                $('#additional_values_edit').append(select_fld_value);
                                $('#additional_fields_edit').append(select_fld);

                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',
                                    processData: false,
                                }).done(function (res) {
                                        var source = res.data.source;
                                        var source_field = '<label>Source</label><select  class="form-control"  name="enquiry_source_id" id="source_list">' +
                                            '<option value="">Select Source</option>';
                                        $.each(source, function (index, value) {
                                            if (value.id == data.enquiry_source_id) {
                                                source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            } else {
                                                source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                            }
                                        });
                                        $('#web_hooks_edit').append(source_field);
                                });

                                $("#rule_assign_mode_id").val(data.assign_mode);
                                $('#rule_assign_mode').show();

                                assignData(data.assign_mode,data.assign_id);
                            }
                            if(data.trigger=="new_lead")
                            {
                                var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var source = res.data.source;
                                        var purpose = res.data.purpose;
                                        
                                    var source_field = '<label>Source</label><select  class="form-control"   name="enquiry_source_id" id="source_list">' +
                                        '<option value="">Select source</option>';
                                    $.each(source, function (index, value) {
                                        if (value.id == data.enquiry_source_id) {
                                            source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        } else {
                                            source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(source_field);
                                    
                                    // Add purpose dropdown for new_lead
                                    var purpose_field = '<label>Purpose</label><select  class="form-control" name="enquiry_purpose_id" id="purpose_list_edit">' +
                                        '<option value="">Select purpose</option>';
                                    $.each(purpose, function (index, value) {
                                        if (value.id == data.enquiry_purpose_id) {
                                            purpose_field = purpose_field + '<option selected value="' + value.id + '">' + value.vchr_purpose + '</option>';
                                        } else {
                                            purpose_field = purpose_field + '<option value="' + value.id + '">' + value.vchr_purpose + '</option>';
                                        }
                                    });
                                    $('#web_hooks_edit').append(purpose_field);
                                    }
                                });


                                $("#rule_assign_mode_id").val(data.assign_mode);
                                $('#rule_assign_mode').show();
                                assignData(data.assign_mode,data.assign_id);
                            }

                        } else if(data.action == "email") {
                                $('#email_div_edit').removeClass('d-none')
                                $('#email_template_code_edit').val(data.email_template);
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {

                                    if (res.status == true) {
                                        var status = res.data.status;
                                         var template = res.data.email_template;
                                        var select_fld = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                            '<option value="">Select Status</option>';
                                        $.each(status, function (index, value) {
                                            if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                                select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                            } else {
                                                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';

                                            }
                                        });
                                        select_fld = select_fld + '</select>';
                                        $('#web_hooks_edit').append(select_fld);

                                        var select_fld_value = '<label> Template</label><select class="form-control select2" name="edit_emplate_id" id="edit_emplate_id">' +
                                        '<option value="">---Select Template---</option>';
                                        $.each(template, function (index, value) {

                                            if (value.id == data.email_template_id) {
                                                select_fld_value = select_fld_value + '<option  selected value="' + value.id + '">' + value.template_title + '</option>';
                                            } else {
                                                select_fld_value = select_fld_value + '<option value="' + value.id + '">' + value.template_title + '</option>';
                                            }
                                        });
                                        select_fld_value = select_fld_value + '</select>';


                                        $('#web_email_edit').append(select_fld_value);
                                    }
                                });

                        } else if(data.action == "add_to_campaign"){
                            var source_field='';
                            $.ajax({
                                url: BASE_URL + '/user/automation-data/?action=campaign',
                                type: 'GET',
                                dataType: 'JSON',
                                processData: false,
                            }).done(function (res) {
                                console.log(res);
                                if (res.status == true) {
                                var source = res.data.source;
                                var campaign = res.data.campaign;
                                var source_field = '<label>Source</label><select  class="form-control"  required name="enquiry_source_id" id="source_list">' +
                                    '<option value="">Select source</option>';
                                $.each(source, function (index, value) {
                                    if (value.id == data.enquiry_source_id) {
                                        source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                    } else {
                                        source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                    }
                                });
                                $('#web_hooks_edit').append(source_field);
                                $('#campaign_div_edit').text('')
                                $('#_edit').removeClass('d-none')
                                $('#campaign_div_edit').removeClass('d-none')
                                var campaign_field = '<label>Campaign</label><select class="form-control" required name="campaign_id" id="campaign_list">' +
                                    '<option value="">Select Campaign</option>';
                                $.each(campaign, function (index, value) {
                                    if (value.id == data.campaign_id) {
                                        campaign_field = campaign_field + '<option selected value="' + value.id + '">' + value.name + '</option>';
                                    } else {
                                        campaign_field = campaign_field + '<option value="' + value.id + '">' + value.name + '</option>';
                                    }
                                });
                                $('#campaign_div_edit').append(campaign_field);

                                }
                            })
                        } else if(data.action == "add_to_data_pool"){
                            var source_field='';
                            $.ajax({
                                url: BASE_URL + '/user/automation-data/?action=data_pool',
                                type: 'GET',
                                dataType: 'JSON',
                                processData: false,
                            }).done(function (res) {
                                console.log(res);
                                if (res.status == true) {
                                var source = res.data.source;
                                var campaign = res.data.campaign;
                                var source_field = '<label>Source</label><select  class="form-control"  required name="enquiry_source_id" id="source_list">' +
                                    '<option value="">Select source</option>';
                                $.each(source, function (index, value) {
                                    if (value.id == data.enquiry_source_id) {
                                        source_field = source_field + '<option selected value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                    } else {
                                        source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
                                    }
                                });
                                $('#web_hooks_edit').append(source_field);
                                $('#campaign_div_edit').text('')
                                $('#_edit').removeClass('d-none')
                                $('#campaign_div_edit').removeClass('d-none')
                                var campaign_field = '<label>Campaign</label><select class="form-control" required name="campaign_id" id="campaign_list">' +
                                    '<option value="">Select Campaign</option>';
                                $.each(campaign, function (index, value) {
                                    if (value.id == data.campaign_id) {
                                        campaign_field = campaign_field + '<option selected value="' + value.id + '">' + value.name + '</option>';
                                    } else {
                                        campaign_field = campaign_field + '<option value="' + value.id + '">' + value.name + '</option>';
                                    }
                                });
                                $('#campaign_div_edit').append(campaign_field);

                                }
                            })
                        } else if(data.action == 'unassign') {
                            var select_fld = '';
                                $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                                }).done(function (res) {
                                    var trainindIdArray = (data.feedback_status_id) ? data.feedback_status_id.split(',') : [];
                                    if (res.status == true) {
                                        var status = res.data.status;
                                        if(data.trigger == 'reassign'){
                                            var select_fld = '<label>Status</label><select  class="form-control"  required name="feedback_status_id" id="feedback_status_list">' +
                                            '<option value="">Select Status</option>';
                                            $.each(status, function (index, value) {
                                                if (value.pk_int_feedback_status_id == data.feedback_status_id) {
                                                    select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                                } else {
                                                    select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';

                                                }
                                            });
                                        }
                                        else{
                                            var select_fld = '<label>Status</label><select  class="form-control select2"  required name="feedback_status_id[]" id="feedback_status_list_edit" multiple>' +
                                            '<option value="">Select Status</option>';
                                            $.each(status, function (index, value) {
                                                if ($.inArray((value.pk_int_feedback_status_id).toString(), trainindIdArray) > -1)
                                                {
                                                    select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                                } else {
                                                    select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                                }
                                            });
                                        }

                                        select_fld = select_fld + '</select>';
                                        $('#web_hooks_edit').append(select_fld);
                                        $('#feedback_status_list_edit').select2();

                                    }
                                });
                        } else if(data.action == 'report') {
                            $.ajax({
                                    url: BASE_URL + '/user/automation-data/?action=api',
                                    type: 'GET',
                                    dataType: 'JSON',

                                    processData: false,
                            }).done(function (res) {
                                var trainindIdArray = (data.feedback_status_id) ? data.feedback_status_id.split(',') : [];
                                if (res.status == true) {
                                    var select_fld = '<label>Type</label><select  class="form-control select2"  required name="notification_type_edit" id="type" >' +
                                    '<option value="">Select Type</option>';

                                    if(data.notification_type==1)
                                    select_fld=select_fld+'<option value="1" selected>With Out Followup Summary</option>';
                                    else
                                    select_fld=select_fld+'<option value="1">With Out Followup Summary</option>';

                                    select_fld=select_fld+'</select>';

                                    var status = res.data.status;
                                    select_fld = select_fld + '<label>Status</label><select  class="form-control select2"  required name="feedback_status_id[]" id="feedback_status_list_edit" multiple>' +
                                        '<option value="">Select Status</option>';
                                    $.each(status, function (index, value) {
                                        if ($.inArray((value.pk_int_feedback_status_id).toString(), trainindIdArray) > -1)
                                        {
                                            select_fld = select_fld + '<option  selected value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                        } else {
                                            select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
                                        }
                                    });
                                    select_fld = select_fld + '</select>';
                                    $('#web_hooks_edit').append(select_fld);
                                    $('#feedback_status_list_edit').select2();
                                }
                            });
                        }
                    }
                }).fail(function (err) {
                }).always(function (com) {
                    $('#auto_rule_data_table').DataTable().ajax.reload(null, false);
                });
            });
        });

        function getAutomationData(action, trigger) {
            $.ajax({
                url: BASE_URL + '/user/automation-data/?action=' + action,
                type: 'GET',
                dataType: 'JSON',
                processData: false,
            }).done(function (res) {

                if (trigger == 'status_change') {
                    var status = res.data.status;
                    statusCreation(status);
                }
                if (trigger == 'source_change') {
                    var source = res.data.source;
                    sourceCreation(source);
                }
                if (trigger == 'purpose_change') {
                    var purpose = res.data.purpose;
                    purposeCreation(purpose);
                }
                if (trigger == 'service_remider') {
                    var service = res.data.service;
                    serviceWithReminderTrigger(service);
                }

                if (action == "task") {
                    var t_category = res.data.action;
                    var agents = res.data.agents;
                    taskGenerationCreation(t_category,agents);
                }
                // $('#web_hooks').append(select_fld);
                // $('#web_hooks').append(fld);
            });
        }

        // Event for adding global variable into the email template
        $(".btn-label").on('click', function() {
            var $txt = jQuery("#email_template_code");
            var caretPos = $txt[0].selectionStart;
            var textAreaTxt = $txt.val();
            var txtToAdd = $(this).html();
            $txt.val(textAreaTxt.substring(0, caretPos) + txtToAdd + textAreaTxt.substring(caretPos) );
        });

        // Event for adding global variable into the email template
        $(".label-ed").on('click', function() {
            var $txt = jQuery("#email_template_code_edit");
            var caretPos = $txt[0].selectionStart;
            var textAreaTxt = $txt.val();
            var txtToAdd = $(this).html();
            $txt.val(textAreaTxt.substring(0, caretPos) + txtToAdd + textAreaTxt.substring(caretPos) );
        });

        // Event for reset the add rule model
        $("#create_rule_modal").on('hide.bs.modal', function(){
            $('#autoRuleAdd').trigger("reset");
            $('#email_div').addClass('d-none')
            $('#assign_mode').hide();
            $('#rule_assign_mode').hide();
            $('#web_hooks').html('');
            $('#web_hooks_assign').html('');
            $('#web_hooks_mail').html('');
            $('#email_template_list').html('');
        });

        // Event for reset the Update rule model
        $("#edit_modal_rule").on('hide.bs.modal', function(){
            $('#autoRuleEdit').trigger("reset");
            $('#email_div_edit').addClass('d-none')
            $('#assign_mode').hide();
            $('#rule_assign_mode').hide();
            $('#web_hooks_edit').html('');
            $('#web_email_edit').html('');
            $('#web_hooks_edit_assign').html('');
        });

        // Event for aditional field values chnage
        $(document).on('change','#field', function () {
                $('#additional_values').html('');
                var field_value = $(this).val();
                var select_fld_value = '<label>Choose Value</label><select class="form-control select2" name="field_value" id="field_value">' +
                                '<option value="">---Select Value---</option>';
                $.each(additional_field, function (index, value) {
                    if(value.id == field_value){
                        $.each(value.values, function (index, value) {
                            select_fld_value = select_fld_value + '<option value="' + value + '">' + value + '</option>';
                        });
                    }
                });
                select_fld_value = select_fld_value + '</select>';
                    $('#additional_values').append(select_fld_value);
                    $('#additional_values').show();

        })

        $(document).on('click','.toggle1', function () {
            var val = $(this).val();
            if(val=='template')
            {
                $.ajax({
                url: BASE_URL + '/user/list-email-template',
                type: 'GET',
                dataType: 'JSON',
                processData: false,
                }).done(function (res) {

                    var select_fld_value = '<label>Choose Template</label><select class="form-control select2" name="emplate_id" id="emplate_id">' +
                                    '<option value="">---Select Template---</option>';
                    $.each(res, function (index, value) {

                        select_fld_value = select_fld_value + '<option value="' + value.id + '">' + value.template_title + '</option>';
                    });
                    select_fld_value = select_fld_value + '</select>';
                    $('#email_div').addClass('d-none');
                    $('#email_template_list').append(select_fld_value);

                })
            }
            else{
                $('#email_div').removeClass('d-none');
                $('#email_template_list').html('');
            }

        })

        function resetAdditionalFields(){
            $('#additional_values').html('').hide();
            $('#additional_fields').html('').hide();
            $('#timer_value').html('').hide();
        }

       function statusWithApi(data,required=null){
            var select_fld = '<label>Status</label><select  class="form-control" '+(!required? 'required' : '')+' name="feedback_status_id" id="feedback_status_list">' +
                '<option value="">Select Status</option>';
            $.each(data, function (index, value) {
                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
            });
            select_fld = select_fld + '</select>';
            var fld = '<label>API</label><input type="text" required autocomplete="off" placeholder="API" class="form-control" name="api">';
            $('#web_hooks').append(select_fld);
            $('#web_hooks').append(fld);
       }
       function sourceWithApi(data,required=null){
            var source_field = '<label>Enquiry Source</label><select  class="form-control"  '+(!required? 'required' : '')+' name="enquiry_source_id" id="whatsapp_template_list">' +
                '<option value="">Select </option>';
            $.each(data, function (index, value) {
                source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
            });
            source_field = source_field + '</select>';
            var fld = '<label>API</label><input type="text" required autocomplete="off" placeholder="API" class="form-control" name="api">';
            $('#web_hooks').append(source_field);
            $('#web_hooks').append(fld);
       }
       function purposeWithApi(data,required=null){
            var purpose_field = '<label>Enquiry Purpose</label><select  class="form-control"  '+(!required? 'required' : '')+' name="enquiry_purpose_id" id="purpose_template_list">' +
                '<option value="">Select </option>';
            $.each(data, function (index, value) {
                purpose_field = purpose_field + '<option value="' + value.id + '">' + value.vchr_purpose + '</option>';
            });
            purpose_field = purpose_field + '</select>';
            var fld = '<label>API</label><input type="text" required autocomplete="off" placeholder="API" class="form-control" name="api">';
            $('#web_hooks').append(purpose_field);
            $('#web_hooks').append(fld);
       }

        function statusCreation(data){
            var select_fld = '<label>Status</label><select  class="form-control" required name="feedback_status_id" id="feedback_status_list">' +
                                    '<option value="">Select Status</option>';
            $.each(data, function (index, value) {
                select_fld = select_fld + '<option value="' + value.pk_int_feedback_status_id + '">' + value.vchr_status + '</option>';
            });
            select_fld = select_fld + '</select>';


            $('#web_hooks').append(select_fld);
        }

        function sourceCreation(data,notRequired = null){

            required = (!notRequired) ? 'required' : '';
            var source_field = '<label>Enquiry Source</label><select  class="form-control" '+required+'  name="enquiry_source_id" id="whatsapp_template_list">' +
                '<option value="">Select </option>';
            $.each(data, function (index, value) {
                source_field = source_field + '<option value="' + value.id + '">' + value.vchr_enquiry_type + '</option>';
            });
            source_field = source_field + '</select>';
            $('#web_hooks').append(source_field);
        }

        function purposeCreation(data,notRequired = null){

            required = (!notRequired) ? 'required' : '';
            var purpose_field = '<label>Enquiry Purpose</label><select  class="form-control" '+required+'  name="enquiry_purpose_id" id="purpose_template_list">' +
                '<option value="">Select </option>';
            $.each(data, function (index, value) {
                purpose_field = purpose_field + '<option value="' + value.id + '">' + value.vchr_purpose + '</option>';
            });
            purpose_field = purpose_field + '</select>';
            $('#web_hooks').append(purpose_field);
        }

        function campaignDivCreation(data,type){
           if(type=='pool')
           {
            var campaign_field = '<label>Data Pool</label><select class="form-control" required name="campaign_id" id="campaign_list">' +
                '<option value="">Select Data Pool</option>';
           }else{
            var campaign_field = '<label>Campaign</label><select class="form-control" required name="campaign_id" id="campaign_list">' +
                '<option value="">Select Campaign</option>';
            }
            $.each(data, function (index, value) {
                campaign_field = campaign_field + '<option value="' + value.id + '">' + value.name + '</option>';
            });
            campaign_field = campaign_field + '</select>';
            $('#campaign_div').append(campaign_field);
        }

        function serviceWithReminderTrigger(data){
            var select_fld = '<label>Service</label><select  class="form-control select2"  name="service[]" multiple id="service">' +
                                    '<option value="">Select Service</option><option value="0">All</option>';
            $.each(data, function (index, value) {
                select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
            });
            select_fld = select_fld + '</select>';

            var select_fld3 = '<label>Reminder before/after</label><select  class="form-control"  required name="reminder_type" id="reminder_type">' +
                '<option value="">Select reminder</option><option value="-">Before Day</option><option value="+">After Day</option>';

            $('#web_hooks').append(select_fld);
            $('#web_hooks').append(select_fld3);
            $('#service').select2();
        }

        function whatsappTemplateGeneration(data){
            var select_fld2 = '<label>Whatsapp Template</label><select  class="form-control"  required name="whatsapp_template_id" id="whatsapp_template_list">' +
                '<option value="">Select Template</option>';
            $.each(data, function (index, value) {
                select_fld2 = select_fld2 + '<option value="' + value.id + '">' + value.vchr_whatsapp_template_title + '</option>';
            });
            select_fld2 = select_fld2 + '</select>';
            var fld2 = '<label>Days</label><input type="number" class="form-control" name="days">';
            $('#web_hooks').append(select_fld2);
            $('#web_hooks').append(fld2);
        }

        function taskGenerationCreation(category,agents){
            var select_fld = '<label>Task Category </label><select  class="form-control"  required name="task_category_id" id="task_category_list">' +
                '<option value="">Select Task Category</option>';
            $.each(category, function (index, value) {
                select_fld = select_fld + '<option value="' + value.id + '">' + value.name + '</option>';
            });
            select_fld = select_fld + '</select>';
            var fld = '<label>Task Title</label><input type="text" autocomplete="off"  placeholder="Title" class="form-control" name="task_title">' +
                '<label>Task Description</label><textarea type="text" autocomplete="off" placeholder="Description" class="form-control" name="task_description"></textarea>';
            var agent_field = '<label>Agent</label><select  class="form-control"  required name="task_assigned_to" id="task_assigned_to_list">' +
                '<option value="">Select Agent</option>';
            $.each(agents, function (index, value) {
                agent_field = agent_field + '<option value="' + value.pk_int_user_id + '">' + value.vchr_user_name + '</option>';
            });
            agent_field = agent_field + '</select>';
            var durnt='<label>Duration(minutes)</label><input type="number" class="form-control" name="duration" value="">';
            $('#web_hooks').append(select_fld);
            $('#web_hooks').append(fld);
            $('#web_hooks').append(agent_field);
            $('#web_hooks').append(durnt);
        }

    </script>
@endpush

