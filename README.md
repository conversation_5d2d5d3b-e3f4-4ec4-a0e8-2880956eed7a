# Getlead Connect

### Ecs usage

Runs ecs code style on the diff of files.

```bash
git diff origin/master --name-only --diff-filter=AMR | grep -E 'php$' | xargs vendor/bin/ecs check  --fix
```


Deployments
[![Laravel Forge Site Deployment Status](https://img.shields.io/endpoint?url=https%3A%2F%2Fforge.laravel.com%2Fsite-badges%2Fdfa357f3-2033-4cca-8465-da9070967e95%3Fdate%3D1%26label%3D1%26commit%3D1&style=for-the-badge)](https://forge.laravel.com/servers/934408/sites/2769021)
